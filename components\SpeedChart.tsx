import React from 'react';
import { View, StyleSheet } from 'react-native';

interface SpeedData {
  value: number;
  color: string;
}

interface SpeedChartProps {
  data: SpeedData[];
}

const SpeedChart: React.FC<SpeedChartProps> = ({ data }) => {
  return (
    <View style={styles.container}>
      {data.map((item, index) => (
        <View 
          key={index}
          style={[
            styles.bar,
            { backgroundColor: item.color }
          ]}
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    height: 30,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  bar: {
    height: 4,
    flex: 1,
    marginHorizontal: 1,
  },
});

export default SpeedChart;