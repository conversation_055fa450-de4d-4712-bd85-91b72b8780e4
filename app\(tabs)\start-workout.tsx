import { useState } from 'react';
import { StyleSheet, View, Text, TouchableOpacity, ScrollView, Image } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ArrowLeft, MapPin, Clock, Play, X } from 'lucide-react-native';
import { router } from 'expo-router';
import { useFitness } from '@/contexts/FitnessContext';

const workoutTypes = [
  { id: 'running', name: 'Running', icon: '🏃‍♂️' },
  { id: 'walking', name: 'Walking', icon: '🚶‍♂️' },
  { id: 'cycling', name: 'Cycling', icon: '🚴‍♂️' },
  { id: 'hiking', name: 'Hiking', icon: '🥾' },
  { id: 'swimming', name: 'Swimming', icon: '🏊‍♂️' },
];

const recentRoutes = [
  { 
    id: 1, 
    name: 'Morning Run', 
    location: 'Central Park', 
    distance: '5.2 km', 
    duration: '28 min',
    image: 'https://images.pexels.com/photos/1868485/pexels-photo-1868485.jpeg'
  },
  { 
    id: 2, 
    name: 'Evening Walk', 
    location: 'Riverside', 
    distance: '3.8 km', 
    duration: '45 min',
    image: 'https://images.pexels.com/photos/1578750/pexels-photo-1578750.jpeg'
  },
  { 
    id: 3, 
    name: 'Weekend Hike', 
    location: 'Mountain Trail', 
    distance: '8.5 km', 
    duration: '1h 42m',
    image: 'https://images.pexels.com/photos/2896669/pexels-photo-2896669.jpeg'
  },
];

export default function StartWorkoutScreen() {
  const { startWorkout } = useFitness();
  const [selectedType, setSelectedType] = useState('running');
  
  const handleStartWorkout = (routeId = null) => {
    startWorkout({
      type: selectedType,
      routeId,
      startTime: new Date(),
    });
    router.replace('/workout');
  };
  
  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.closeButton}>
          <X size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Start Workout</Text>
        <View style={styles.placeholder} />
      </View>
      
      <ScrollView contentContainerStyle={styles.content}>
        <Text style={styles.sectionTitle}>Workout Type</Text>
        <ScrollView 
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.workoutTypesContainer}
        >
          {workoutTypes.map((type) => (
            <TouchableOpacity
              key={type.id}
              style={[
                styles.workoutTypeButton,
                selectedType === type.id && styles.selectedWorkoutType
              ]}
              onPress={() => setSelectedType(type.id)}
            >
              <Text style={styles.workoutTypeIcon}>{type.icon}</Text>
              <Text 
                style={[
                  styles.workoutTypeName,
                  selectedType === type.id && styles.selectedWorkoutTypeName
                ]}
              >
                {type.name}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
        
        <Text style={styles.sectionTitle}>Quick Start</Text>
        <TouchableOpacity 
          style={styles.quickStartButton}
          onPress={() => handleStartWorkout()}
        >
          <View style={styles.quickStartContent}>
            <Text style={styles.quickStartTitle}>Start Now</Text>
            <Text style={styles.quickStartSubtitle}>Track your workout without a route</Text>
          </View>
          <View style={styles.startIconContainer}>
            <Play size={20} color="#fff" fill="#fff" />
          </View>
        </TouchableOpacity>
        
        <View style={styles.recentRoutesHeader}>
          <Text style={styles.sectionTitle}>Recent Routes</Text>
          <TouchableOpacity>
            <Text style={styles.viewAllText}>View All</Text>
          </TouchableOpacity>
        </View>
        
        {recentRoutes.map((route) => (
          <TouchableOpacity 
            key={route.id}
            style={styles.routeCard}
            onPress={() => handleStartWorkout(route.id)}
          >
            <Image source={{ uri: route.image }} style={styles.routeImage} />
            <View style={styles.routeContent}>
              <Text style={styles.routeName}>{route.name}</Text>
              <View style={styles.routeDetails}>
                <View style={styles.routeDetailItem}>
                  <MapPin size={16} color="#666" />
                  <Text style={styles.routeDetailText}>{route.location}</Text>
                </View>
                <View style={styles.routeDetailItem}>
                  <Clock size={16} color="#666" />
                  <Text style={styles.routeDetailText}>{route.duration}</Text>
                </View>
              </View>
              <View style={styles.routeDistanceContainer}>
                <Text style={styles.routeDistance}>{route.distance}</Text>
              </View>
            </View>
          </TouchableOpacity>
        ))}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  closeButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontFamily: 'InterBold',
    color: '#333',
  },
  placeholder: {
    width: 40,
  },
  content: {
    padding: 16,
    paddingBottom: 80,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'InterBold',
    color: '#333',
    marginTop: 16,
    marginBottom: 12,
  },
  workoutTypesContainer: {
    paddingBottom: 8,
  },
  workoutTypeButton: {
    alignItems: 'center',
    marginRight: 16,
    backgroundColor: '#f9f9f9',
    borderRadius: 16,
    padding: 16,
    minWidth: 90,
  },
  selectedWorkoutType: {
    backgroundColor: 'rgba(0, 196, 140, 0.1)',
  },
  workoutTypeIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  workoutTypeName: {
    fontSize: 14,
    fontFamily: 'InterMedium',
    color: '#666',
  },
  selectedWorkoutTypeName: {
    color: '#00C48C',
    fontFamily: 'InterBold',
  },
  quickStartButton: {
    backgroundColor: '#f9f9f9',
    borderRadius: 16,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  quickStartContent: {
    flex: 1,
  },
  quickStartTitle: {
    fontSize: 16,
    fontFamily: 'InterBold',
    color: '#333',
    marginBottom: 4,
  },
  quickStartSubtitle: {
    fontSize: 14,
    fontFamily: 'InterRegular',
    color: '#666',
  },
  startIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#00C48C',
    justifyContent: 'center',
    alignItems: 'center',
  },
  recentRoutesHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  viewAllText: {
    fontSize: 14,
    fontFamily: 'InterMedium',
    color: '#00C48C',
  },
  routeCard: {
    backgroundColor: '#f9f9f9',
    borderRadius: 16,
    marginBottom: 16,
    overflow: 'hidden',
  },
  routeImage: {
    width: '100%',
    height: 120,
  },
  routeContent: {
    padding: 16,
  },
  routeName: {
    fontSize: 16,
    fontFamily: 'InterBold',
    color: '#333',
    marginBottom: 8,
  },
  routeDetails: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  routeDetailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  routeDetailText: {
    fontSize: 14,
    fontFamily: 'InterRegular',
    color: '#666',
    marginLeft: 4,
  },
  routeDistanceContainer: {
    position: 'absolute',
    right: 16,
    bottom: 16,
    backgroundColor: '#00C48C',
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 8,
  },
  routeDistance: {
    fontSize: 12,
    fontFamily: 'InterBold',
    color: '#fff',
  },
});