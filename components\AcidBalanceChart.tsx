import React from 'react';
import { View, StyleSheet } from 'react-native';

const AcidBalanceChart: React.FC = () => {
  const points = [
    { x: 0, y: 0, active: false },
    { x: 1, y: 0, active: false },
    { x: 2, y: 0, active: false },
    { x: 3, y: 0, active: false },
    { x: 0, y: 1, active: false },
    { x: 1, y: 1, active: true },
    { x: 2, y: 1, active: false },
    { x: 3, y: 1, active: false },
    { x: 0, y: 2, active: false },
    { x: 1, y: 2, active: false },
    { x: 2, y: 2, active: true },
    { x: 3, y: 2, active: false },
    { x: 0, y: 3, active: false },
    { x: 1, y: 3, active: false },
    { x: 2, y: 3, active: false },
    { x: 3, y: 3, active: true },
  ];

  return (
    <View style={styles.container}>
      {points.map((point, index) => (
        <View
          key={index}
          style={[
            styles.point,
            point.active && styles.activePoint,
          ]}
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    width: 140,
    height: 40,
    justifyContent: 'space-between',
  },
  point: {
    width: 8,
    height: 8,
    borderRadius: 4,
    margin: 8,
    backgroundColor: '#eee',
  },
  activePoint: {
    backgroundColor: '#00C48C',
  },
});

export default AcidBalanceChart;