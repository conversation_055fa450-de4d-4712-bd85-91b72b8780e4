import React from 'react';
import { View, StyleSheet } from 'react-native';
import Svg, { Path } from 'react-native-svg';
import { line, curveBasis } from 'd3-shape';

const BrainActivityChart: React.FC = () => {
  // Sample brain wave data
  const data = [
    { x: 0, y: 50 },
    { x: 5, y: 30 },
    { x: 10, y: 60 },
    { x: 15, y: 40 },
    { x: 20, y: 70 },
    { x: 25, y: 50 },
    { x: 30, y: 80 },
    { x: 35, y: 45 },
    { x: 40, y: 65 },
    { x: 45, y: 55 },
    { x: 50, y: 75 },
    { x: 55, y: 35 },
    { x: 60, y: 60 },
    { x: 65, y: 50 },
    { x: 70, y: 70 },
    { x: 75, y: 45 },
    { x: 80, y: 65 },
    { x: 85, y: 55 },
    { x: 90, y: 75 },
    { x: 95, y: 40 },
    { x: 100, y: 60 },
  ];

  const width = 140;
  const height = 40;

  const path = line<{ x: number, y: number }>()
    .x(d => (d.x / 100) * width)
    .y(d => (d.y / 100) * height)
    .curve(curveBasis)(data);

  return (
    <View style={styles.container}>
      <Svg width={width} height={height}>
        <Path
          d={path || ''}
          stroke="#FF9C9C"
          strokeWidth={2}
          fill="none"
        />
      </Svg>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 40,
    justifyContent: 'center',
  },
});

export default BrainActivityChart;