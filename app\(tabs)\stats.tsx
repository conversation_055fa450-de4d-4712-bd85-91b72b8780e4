import { StyleSheet, View, Text, ScrollView, TouchableOpacity } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ArrowLeft, MoveVertical as MoreVertical, Flame, Timer } from 'lucide-react-native';
import { router } from 'expo-router';
import { useFitness } from '@/contexts/FitnessContext';
import StepsLine<PERSON>hart from '@/components/StepsLineChart';
import CaloriesBarChart from '@/components/CaloriesBarChart';
import SpeedChart from '@/components/SpeedChart';

export default function StatsScreen() {
  const { walkingStats } = useFitness();
  
  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()}>
          <ArrowLeft size={24} color="#333" />
        </TouchableOpacity>
        <MoreVertical size={24} color="#333" />
      </View>
      
      <ScrollView contentContainerStyle={styles.content}>
        <View style={styles.titleContainer}>
          <View style={styles.iconContainer}>
            <Text style={styles.icon}>👣</Text>
          </View>
          <View>
            <Text style={styles.title}>Walking steps</Text>
            <Text style={styles.subtitle}>See your Walking Statistics</Text>
          </View>
        </View>
        
        <View style={styles.metricsContainer}>
          <View style={styles.metricCard}>
            <Text style={styles.metricLabel}>Total Distance</Text>
            <View style={styles.metricValueContainer}>
              <Text style={styles.metricValue}>{walkingStats.totalDistance}</Text>
              <Text style={styles.metricUnit}>km/h</Text>
            </View>
          </View>
          <View style={styles.metricCard}>
            <Text style={styles.metricLabel}>Total Steps</Text>
            <Text style={styles.metricValue}>{walkingStats.totalSteps}</Text>
          </View>
        </View>
        
        <View style={styles.chartContainer}>
          <StepsLineChart data={walkingStats.stepsData} />
        </View>
        
        <View style={styles.smallMetricsContainer}>
          <View style={styles.smallMetricCard}>
            <View style={styles.smallMetricHeader}>
              <View style={styles.metricIconContainer}>
                <Flame size={18} color="#FF5B5B" />
              </View>
              <Text style={styles.smallMetricLabel}>Burnt Calories</Text>
            </View>
            <Text style={styles.smallMetricValue}>{walkingStats.calories} <Text style={styles.smallMetricUnit}>kCal</Text></Text>
            <CaloriesBarChart data={walkingStats.caloriesData} />
          </View>
          
          <View style={styles.smallMetricCard}>
            <View style={styles.smallMetricHeader}>
              <View style={[styles.metricIconContainer, styles.speedIconContainer]}>
                <Timer size={18} color="#00C48C" />
              </View>
              <Text style={styles.smallMetricLabel}>Speed Average</Text>
            </View>
            <Text style={styles.smallMetricValue}>{walkingStats.speed} <Text style={styles.smallMetricUnit}>km/h</Text></Text>
            <SpeedChart data={walkingStats.speedData} />
          </View>
        </View>
        
        <View style={styles.weeklyStatsContainer}>
          <Text style={styles.weeklyStatsTitle}>Weekly Statistics</Text>
          <View style={styles.weeklyStatsContent}>
            <View style={styles.statColumn}>
              <Text style={styles.statValue}>42.8</Text>
              <Text style={styles.statLabel}>km walked</Text>
            </View>
            <View style={styles.statSeparator} />
            <View style={styles.statColumn}>
              <Text style={styles.statValue}>7,234</Text>
              <Text style={styles.statLabel}>avg steps</Text>
            </View>
            <View style={styles.statSeparator} />
            <View style={styles.statColumn}>
              <Text style={styles.statValue}>3,200</Text>
              <Text style={styles.statLabel}>kcal burnt</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
  },
  content: {
    padding: 16,
    paddingBottom: 80,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  icon: {
    fontSize: 24,
  },
  title: {
    fontSize: 22,
    fontFamily: 'InterBold',
    color: '#333',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 14,
    fontFamily: 'InterRegular',
    color: '#666',
  },
  metricsContainer: {
    flexDirection: 'row',
    marginBottom: 24,
    gap: 16,
  },
  metricCard: {
    flex: 1,
    backgroundColor: '#f9f9f9',
    borderRadius: 16,
    padding: 16,
  },
  metricLabel: {
    fontSize: 14,
    fontFamily: 'InterRegular',
    color: '#666',
    marginBottom: 8,
  },
  metricValueContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  metricValue: {
    fontSize: 24,
    fontFamily: 'InterBold',
    color: '#333',
  },
  metricUnit: {
    fontSize: 14,
    fontFamily: 'InterRegular',
    color: '#666',
    marginLeft: 4,
  },
  chartContainer: {
    backgroundColor: '#f9f9f9',
    borderRadius: 16,
    padding: 16,
    marginBottom: 24,
    height: 240,
  },
  smallMetricsContainer: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 24,
  },
  smallMetricCard: {
    flex: 1,
    backgroundColor: '#f9f9f9',
    borderRadius: 16,
    padding: 16,
  },
  smallMetricHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  metricIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 91, 91, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  speedIconContainer: {
    backgroundColor: 'rgba(0, 196, 140, 0.1)',
  },
  smallMetricLabel: {
    fontSize: 14,
    fontFamily: 'InterRegular',
    color: '#666',
  },
  smallMetricValue: {
    fontSize: 20,
    fontFamily: 'InterBold',
    color: '#333',
    marginBottom: 12,
  },
  smallMetricUnit: {
    fontSize: 14,
    fontFamily: 'InterRegular',
    color: '#666',
  },
  weeklyStatsContainer: {
    backgroundColor: '#f9f9f9',
    borderRadius: 16,
    padding: 16,
  },
  weeklyStatsTitle: {
    fontSize: 16,
    fontFamily: 'InterBold',
    color: '#333',
    marginBottom: 16,
  },
  weeklyStatsContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statColumn: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontFamily: 'InterBold',
    color: '#333',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'InterRegular',
    color: '#666',
  },
  statSeparator: {
    width: 1,
    height: '100%',
    backgroundColor: '#e0e0e0',
  },
});