import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import Svg, { Path, Line, Circle, Defs, LinearGradient, Stop } from 'react-native-svg';
import { line, curveBasis } from 'd3-shape';

interface StepsData {
  hour: string;
  steps: number;
}

interface StepsLineChartProps {
  data: StepsData[];
}

const StepsLineChart: React.FC<StepsLineChartProps> = ({ data }) => {
  const maxSteps = Math.max(...data.map(d => d.steps));
  const width = 320;
  const height = 180;
  const padding = { top: 20, right: 10, bottom: 30, left: 30 };
  const chartWidth = width - padding.left - padding.right;
  const chartHeight = height - padding.top - padding.bottom;

  // Create the line path
  const points = data.map((d, i) => ({
    x: (i / (data.length - 1)) * chartWidth + padding.left,
    y: chartHeight - (d.steps / maxSteps) * chartHeight + padding.top,
    steps: d.steps,
    hour: d.hour,
  }));

  const highlight = data.findIndex(d => d.hour === '06:00');
  
  const linePath = line<{ x: number, y: number }>()
    .x(d => d.x)
    .y(d => d.y)
    .curve(curveBasis)(points);

  // Create the area path for gradient
  const areaPath = line<{ x: number, y: number }>()
    .x(d => d.x)
    .y(d => d.y)
    .curve(curveBasis)(points.concat([
      { x: points[points.length - 1].x, y: height - padding.bottom },
      { x: points[0].x, y: height - padding.bottom },
    ]));

  return (
    <View style={styles.container}>
      <Svg width={width} height={height}>
        <Defs>
          <LinearGradient id="areaGradient" x1="0" y1="0" x2="0" y2="1">
            <Stop offset="0" stopColor="#00C48C" stopOpacity="0.3" />
            <Stop offset="1" stopColor="#00C48C" stopOpacity="0" />
          </LinearGradient>
        </Defs>

        {/* Horizontal grid lines */}
        {[0, 1, 2, 3].map((_, i) => (
          <Line
            key={`grid-${i}`}
            x1={padding.left}
            y1={padding.top + (chartHeight / 3) * i}
            x2={width - padding.right}
            y2={padding.top + (chartHeight / 3) * i}
            stroke="#eee"
            strokeWidth={1}
          />
        ))}

        {/* Area gradient */}
        <Path
          d={areaPath || ''}
          fill="url(#areaGradient)"
        />

        {/* Line */}
        <Path
          d={linePath || ''}
          stroke="#00C48C"
          strokeWidth={2}
          fill="none"
        />

        {/* Highlight point */}
        {highlight >= 0 && (
          <>
            <Line
              x1={points[highlight].x}
              y1={padding.top}
              x2={points[highlight].x}
              y2={height - padding.bottom}
              stroke="#FF5B5B"
              strokeWidth={1}
              strokeDasharray="3,3"
            />
            <Circle
              cx={points[highlight].x}
              cy={points[highlight].y}
              r={6}
              fill="#FF5B5B"
            />
          </>
        )}

        {/* X-axis labels */}
        {points.filter((_, i) => i % 2 === 0).map((point, i) => (
          <Text
            key={`label-${i}`}
            x={point.x}
            y={height - 10}
            fontSize={10}
            textAnchor="middle"
            fill="#666"
          >
            {point.hour}
          </Text>
        ))}
      </Svg>

      <View style={styles.labelOverlay}>
        <Text style={styles.highlightValue}>2400</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  labelOverlay: {
    position: 'absolute',
    left: 140,
    top: 80,
  },
  highlightValue: {
    color: '#FF5B5B',
    fontFamily: 'InterBold',
    fontSize: 14,
  },
});

export default StepsLineChart;