import React from 'react';
import { View, StyleSheet } from 'react-native';

const ActivityBarChart: React.FC = () => {
  const data = [4, 12, 8, 20, 14, 6, 10, 18, 7, 15, 9, 5];
  const maxValue = Math.max(...data);

  return (
    <View style={styles.container}>
      {data.map((value, index) => (
        <View 
          key={index} 
          style={[
            styles.bar, 
            { 
              height: (value / maxValue) * 100,
              backgroundColor: index === 4 ? '#00C48C' : '#eee',
            }
          ]} 
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    height: 100,
    justifyContent: 'space-between',
  },
  bar: {
    width: 16,
    borderRadius: 8,
    backgroundColor: '#eee',
  },
});

export default ActivityBarChart;