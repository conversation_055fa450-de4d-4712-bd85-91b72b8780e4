import { useEffect } from 'react';
import { StyleSheet, View, Text, ActivityIndicator } from 'react-native';
import { useAuth } from '@/contexts/AuthContext';
import { router } from 'expo-router';

export default function Index() {
  const { isLoggedIn, isInitializing } = useAuth();

  useEffect(() => {
    if (!isInitializing) {
      if (isLoggedIn) {
        router.replace('/(tabs)');
      } else {
        router.replace('/(auth)/login');
      }
    }
  }, [isLoggedIn, isInitializing]);

  if (isInitializing) {
    return (
      <View style={styles.container}>
        <Text style={styles.logo}>FitTrack</Text>
        <ActivityIndicator size="large" color="#00C48C" />
      </View>
    );
  }

  return null;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 20,
  },
  logo: {
    fontSize: 32,
    fontFamily: 'InterBold',
    marginBottom: 20,
  },
});