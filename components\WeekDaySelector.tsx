import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';

interface WeekDaySelectorProps {
  days: string[];
  selectedDay: number;
  currentDay: number;
  onSelectDay: (day: number) => void;
}

const WeekDaySelector: React.FC<WeekDaySelectorProps> = ({
  days,
  selectedDay,
  currentDay,
  onSelectDay,
}) => {
  return (
    <View style={styles.container}>
      {days.map((day, index) => (
        <TouchableOpacity
          key={index}
          style={[
            styles.dayButton,
            selectedDay === index && styles.selectedDay,
          ]}
          onPress={() => onSelectDay(index)}
        >
          <Text
            style={[
              styles.dayText,
              selectedDay === index && styles.selectedDayText,
              index === currentDay && styles.currentDayText,
            ]}
          >
            {day}
          </Text>
          <View
            style={[
              styles.dayDot,
              index === currentDay && styles.currentDayDot,
              selectedDay === index && styles.selectedDayDot,
            ]}
          />
        </TouchableOpacity>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dayButton: {
    alignItems: 'center',
    paddingVertical: 4,
  },
  selectedDay: {
    // Additional styles for selected day button
  },
  dayText: {
    fontSize: 12,
    fontFamily: 'InterRegular',
    color: '#999',
    marginBottom: 4,
  },
  selectedDayText: {
    fontFamily: 'InterBold',
    color: '#00C48C',
  },
  currentDayText: {
    fontFamily: 'InterMedium',
  },
  dayDot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: 'transparent',
  },
  currentDayDot: {
    backgroundColor: '#999',
  },
  selectedDayDot: {
    backgroundColor: '#00C48C',
  },
});

export default WeekDaySelector;