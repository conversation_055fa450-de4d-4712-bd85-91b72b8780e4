import { StyleSheet, View, Text, TouchableOpacity, Image, Platform } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { ArrowLeft, Pause, Plus, Minus, Navigation } from 'lucide-react-native';
import { useFitness } from '@/contexts/FitnessContext';
import { useState } from 'react';
import WorkoutInfo from '@/components/WorkoutInfo';

// Conditionally import MapView only for native platforms
let MapView: any;
let Marker: any;
let Polyline: any;

if (Platform.OS !== 'web') {
  const Maps = require('react-native-maps');
  MapView = Maps.default;
  Marker = Maps.Marker;
  Polyline = Maps.Polyline;
}

const WebMapFallback = ({ children }: { children: React.ReactNode }) => (
  <View style={styles.webMapFallback}>
    <Text style={styles.webMapText}>Map view is not available on web</Text>
    <Text style={styles.webMapSubtext}>Please use the mobile app for full map functionality</Text>
  </View>
);

export default function WorkoutScreen() {
  const { currentWorkout } = useFitness();
  const [mapType, setMapType] = useState('standard');
  
  if (!currentWorkout) {
    return (
      <SafeAreaView style={styles.emptyContainer}>
        <Text style={styles.emptyTitle}>No Active Workout</Text>
        <Text style={styles.emptySubtitle}>Start a new workout to track your progress</Text>
        <TouchableOpacity style={styles.startButton}>
          <Text style={styles.startButtonText}>Start Workout</Text>
        </TouchableOpacity>
      </SafeAreaView>
    );
  }
  
  const MapComponent = Platform.OS !== 'web' ? (
    <MapView
      style={styles.map}
      mapType={mapType}
      region={{
        latitude: currentWorkout.currentLocation.latitude,
        longitude: currentWorkout.currentLocation.longitude,
        latitudeDelta: 0.005,
        longitudeDelta: 0.005,
      }}
    >
      <Marker
        coordinate={currentWorkout.currentLocation}
        title="Current Location"
      />
      {currentWorkout.routeCoordinates.length > 0 && (
        <Polyline
          coordinates={currentWorkout.routeCoordinates}
          strokeColor="#00C48C"
          strokeWidth={4}
        />
      )}
    </MapView>
  ) : (
    <WebMapFallback>
      <Text>Map Content</Text>
    </WebMapFallback>
  );
  
  return (
    <View style={styles.container}>
      {MapComponent}
      
      <SafeAreaView style={styles.overlay} edges={['top']}>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton}>
            <ArrowLeft size={24} color="#fff" />
          </TouchableOpacity>
          <View style={styles.routeInfo}>
            <View style={styles.routeImageContainer}>
              <Image 
                source={{ uri: 'https://images.pexels.com/photos/2896669/pexels-photo-2896669.jpeg' }} 
                style={styles.routeImage} 
              />
            </View>
            <View style={styles.routeDetails}>
              <Text style={styles.routeName}>Dihos National Park</Text>
              <Text style={styles.routeFrom}>From my House</Text>
            </View>
          </View>
          <TouchableOpacity style={styles.pauseButton}>
            <Pause size={24} color="#fff" />
          </TouchableOpacity>
        </View>
      </SafeAreaView>
      
      <WorkoutInfo workout={currentWorkout} />
      
      {Platform.OS !== 'web' && (
        <View style={styles.controls}>
          <TouchableOpacity style={styles.controlButton}>
            <Navigation size={24} color="#00C48C" />
          </TouchableOpacity>
          
          <View style={styles.zoomControls}>
            <TouchableOpacity style={styles.zoomButton}>
              <Plus size={20} color="#333" />
            </TouchableOpacity>
            <TouchableOpacity style={styles.zoomButton}>
              <Minus size={20} color="#333" />
            </TouchableOpacity>
          </View>
        </View>
      )}
      
      {Platform.OS !== 'web' && (
        <View style={styles.nextPointContainer}>
          <View style={styles.nextPointIcon}>
            <ArrowLeft size={20} color="#00C48C" style={{ transform: [{ rotate: '135deg' }] }} />
          </View>
          <View style={styles.nextPointInfo}>
            <Text style={styles.nextPointDistance}>250m</Text>
            <Text style={styles.nextPointDescription}>Straight road</Text>
          </View>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#fff',
  },
  emptyTitle: {
    fontSize: 24,
    fontFamily: 'InterBold',
    marginBottom: 8,
    color: '#333',
  },
  emptySubtitle: {
    fontSize: 16,
    textAlign: 'center',
    color: '#666',
    marginBottom: 32,
    fontFamily: 'InterRegular',
  },
  startButton: {
    backgroundColor: '#00C48C',
    paddingVertical: 16,
    paddingHorizontal: 32,
    borderRadius: 12,
  },
  startButtonText: {
    color: '#fff',
    fontSize: 16,
    fontFamily: 'InterBold',
  },
  map: {
    ...StyleSheet.absoluteFillObject,
  },
  webMapFallback: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
  },
  webMapText: {
    fontSize: 18,
    fontFamily: 'InterBold',
    color: '#333',
    marginBottom: 8,
  },
  webMapSubtext: {
    fontSize: 14,
    fontFamily: 'InterRegular',
    color: '#666',
    textAlign: 'center',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  routeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 16,
  },
  routeImageContainer: {
    width: 36,
    height: 36,
    borderRadius: 8,
    overflow: 'hidden',
    marginRight: 8,
  },
  routeImage: {
    width: '100%',
    height: '100%',
  },
  routeDetails: {
    flex: 1,
  },
  routeName: {
    color: '#fff',
    fontSize: 14,
    fontFamily: 'InterMedium',
  },
  routeFrom: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 12,
    fontFamily: 'InterRegular',
  },
  pauseButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 91, 91, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  controls: {
    position: 'absolute',
    right: 16,
    bottom: 220,
    alignItems: 'center',
  },
  controlButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  zoomControls: {
    backgroundColor: '#fff',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  zoomButton: {
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  nextPointContainer: {
    position: 'absolute',
    right: 24,
    bottom: 140,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  nextPointIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 196, 140, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  nextPointInfo: {
    flex: 1,
  },
  nextPointDistance: {
    fontSize: 16,
    fontFamily: 'InterBold',
    color: '#333',
  },
  nextPointDescription: {
    fontSize: 14,
    fontFamily: 'InterRegular',
    color: '#666',
  },
});