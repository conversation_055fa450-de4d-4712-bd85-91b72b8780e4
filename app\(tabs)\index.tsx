import { StyleSheet, View, Text, ScrollView, Image, TouchableOpacity } from 'react-native';
import { useFitness } from '@/contexts/FitnessContext';
import { useAuth } from '@/contexts/AuthContext';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Heart, Activity, Brain, MoveVertical as MoreVertical, Calendar } from 'lucide-react-native';
import CircularProgress from '@/components/CircularProgress';
import WeekDaySelector from '@/components/WeekDaySelector';
import ActivityBarChart from '@/components/ActivityBarChart';
import HeartRateGraph from '@/components/HeartRateGraph';
import AcidBalanceChart from '@/components/AcidBalanceChart';
import BrainActivityChart from '@/components/BrainActivityChart';

export default function HomeScreen() {
  const { user } = useAuth();
  const { dailyStats, selectedDay, setSelectedDay } = useFitness();

  const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
  const currentDay = new Date().getDay();

  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.header}>
          <View style={styles.welcomeContainer}>
            <Text style={styles.welcomeText}>Welcome</Text>
            <Text style={styles.nameText}>{user?.name}</Text>
          </View>
          <View style={styles.profileContainer}>
            <Image
              source={{ uri: user?.profileImage || 'https://images.pexels.com/photos/614810/pexels-photo-614810.jpeg' }}
              style={styles.profileImage}
            />
            <TouchableOpacity style={styles.moreButton}>
              <MoreVertical size={24} color="#333" />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.statsCard}>
          <CircularProgress 
            percentage={dailyStats.stepsPercentage}
            size={120}
            strokeWidth={12}
            progressColor="#00C48C"
            steps={dailyStats.steps}
          />

          <View style={styles.weekContainer}>
            <WeekDaySelector 
              days={days}
              selectedDay={selectedDay}
              currentDay={currentDay}
              onSelectDay={setSelectedDay}
            />
          </View>
        </View>

        <Text style={styles.sectionTitle}>Personal Health</Text>
        
        <View style={styles.healthCardsContainer}>
          <View style={styles.healthCard}>
            <View style={styles.healthCardHeader}>
              <View style={styles.healthIconContainer}>
                <Heart size={20} color="#FF5B5B" />
              </View>
              <Text style={styles.healthCardTitle}>Cardiac{'\n'}Rhythm</Text>
            </View>
            <View style={styles.healthCardValue}>
              <Text style={styles.valueText}>95</Text>
              <Text style={styles.unitText}>bpm</Text>
            </View>
            <HeartRateGraph />
          </View>
          <View style={styles.healthCard}>
            <View style={styles.healthCardHeader}>
              <View style={[styles.healthIconContainer, styles.acidIconContainer]}>
                <Activity size={20} color="#FF7676" />
              </View>
              <Text style={styles.healthCardTitle}>Acid{'\n'}Balance</Text>
            </View>
            <View style={styles.healthCardValue}>
              <Text style={styles.valueText}>14</Text>
              <Text style={styles.unitText}>pH</Text>
            </View>
            <AcidBalanceChart />
          </View>
        </View>

        <View style={styles.brainActivityContainer}>
          <View style={styles.brainCard}>
            <View style={styles.healthCardHeader}>
              <View style={[styles.healthIconContainer, styles.brainIconContainer]}>
                <Brain size={20} color="#FF9C9C" />
              </View>
              <Text style={styles.healthCardTitle}>Brain{'\n'}Activity</Text>
            </View>
            <BrainActivityChart />
          </View>
          <TouchableOpacity style={styles.exerciseButton}>
            <Text style={styles.exerciseButtonText}>Exercise</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.activityContainer}>
          <View style={styles.activityHeader}>
            <Text style={styles.activityTitle}>Activity</Text>
            <Text style={styles.activityDay}>Thursday</Text>
          </View>
          <ActivityBarChart />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContainer: {
    padding: 16,
    paddingBottom: 80,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
    marginTop: 8,
  },
  welcomeContainer: {
    flex: 1,
  },
  welcomeText: {
    fontSize: 16,
    color: '#888',
    fontFamily: 'InterRegular',
  },
  nameText: {
    fontSize: 18,
    fontFamily: 'InterBold',
    color: '#333',
  },
  profileContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  profileImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 16,
  },
  moreButton: {
    padding: 4,
  },
  statsCard: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
  },
  weekContainer: {
    flex: 1,
    marginLeft: 16,
  },
  sectionTitle: {
    fontSize: 22,
    fontFamily: 'InterBold',
    color: '#333',
    marginBottom: 16,
  },
  healthCardsContainer: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 16,
  },
  healthCard: {
    flex: 1,
    backgroundColor: '#f9f9f9',
    borderRadius: 16,
    padding: 16,
  },
  healthCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  healthIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: 'rgba(255, 91, 91, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  acidIconContainer: {
    backgroundColor: 'rgba(255, 118, 118, 0.1)',
  },
  brainIconContainer: {
    backgroundColor: 'rgba(255, 156, 156, 0.1)',
  },
  healthCardTitle: {
    fontSize: 14,
    fontFamily: 'InterRegular',
    color: '#666',
  },
  healthCardValue: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    marginBottom: 12,
  },
  valueText: {
    fontSize: 28,
    fontFamily: 'InterBold',
    color: '#333',
  },
  unitText: {
    fontSize: 16,
    fontFamily: 'InterRegular',
    color: '#666',
    marginLeft: 4,
    marginBottom: 4,
  },
  brainActivityContainer: {
    flexDirection: 'row',
    gap: 16,
    marginBottom: 24,
  },
  brainCard: {
    flex: 1,
    backgroundColor: '#f9f9f9',
    borderRadius: 16,
    padding: 16,
  },
  exerciseButton: {
    width: 84,
    height: 84,
    borderRadius: 42,
    backgroundColor: '#000',
    justifyContent: 'center',
    alignItems: 'center',
    alignSelf: 'center',
  },
  exerciseButtonText: {
    color: '#fff',
    fontFamily: 'InterMedium',
    fontSize: 14,
  },
  activityContainer: {
    backgroundColor: '#f9f9f9',
    borderRadius: 16,
    padding: 16,
  },
  activityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  activityTitle: {
    fontSize: 16,
    fontFamily: 'InterBold',
    color: '#333',
  },
  activityDay: {
    fontSize: 16,
    fontFamily: 'InterRegular',
    color: '#666',
  },
});