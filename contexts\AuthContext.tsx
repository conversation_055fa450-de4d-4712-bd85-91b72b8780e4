import React, { createContext, useContext, useState, useEffect } from 'react';
import * as SecureStore from 'expo-secure-store';
import { router } from 'expo-router';

interface User {
  id: string;
  name: string;
  email: string;
  profileImage?: string;
}

interface AuthContextType {
  user: User | null;
  isLoggedIn: boolean;
  isInitializing: boolean;
  login: (email: string, password: string) => Promise<void>;
  register: (name: string, email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isInitializing, setIsInitializing] = useState(true);

  useEffect(() => {
    const loadUser = async () => {
      try {
        const userJson = await SecureStore.getItemAsync('user');
        if (userJson) {
          setUser(JSON.parse(userJson));
        }
      } catch (error) {
        console.error('Failed to load user from storage', error);
      } finally {
        setIsInitializing(false);
      }
    };

    loadUser();
  }, []);

  const login = async (email: string, password: string) => {
    // In a real app, this would validate credentials with a server
    // For demo purposes, we'll simulate a successful login
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock user data
      const mockUser = {
        id: '123456',
        name: 'Alex Northam',
        email: email,
        profileImage: 'https://images.pexels.com/photos/614810/pexels-photo-614810.jpeg',
      };
      
      setUser(mockUser);
      await SecureStore.setItemAsync('user', JSON.stringify(mockUser));
      router.replace('/(tabs)');
    } catch (error) {
      console.error('Login failed', error);
      throw new Error('Login failed');
    }
  };

  const register = async (name: string, email: string, password: string) => {
    // In a real app, this would create a new user on the server
    // For demo purposes, we'll simulate a successful registration
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Create mock user
      const mockUser = {
        id: '123456',
        name: name,
        email: email,
        profileImage: 'https://images.pexels.com/photos/614810/pexels-photo-614810.jpeg',
      };
      
      setUser(mockUser);
      await SecureStore.setItemAsync('user', JSON.stringify(mockUser));
      router.replace('/(tabs)');
    } catch (error) {
      console.error('Registration failed', error);
      throw new Error('Registration failed');
    }
  };

  const logout = async () => {
    try {
      await SecureStore.deleteItemAsync('user');
      setUser(null);
      router.replace('/(auth)/login');
    } catch (error) {
      console.error('Logout failed', error);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoggedIn: !!user,
        isInitializing,
        login,
        register,
        logout,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};