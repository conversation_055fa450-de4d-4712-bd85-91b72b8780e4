export interface StepsData {
  hour: string;
  steps: number;
}

export interface SpeedData {
  value: number;
  color: string;
}

export interface DailyStats {
  steps: number;
  stepsGoal: number;
  stepsPercentage: number;
  distance: number;
  calories: number;
  activeMinutes: number;
}

export interface WalkingStats {
  totalDistance: string;
  totalSteps: string;
  calories: string;
  speed: string;
  stepsData: StepsData[];
  caloriesData: { value: number }[];
  speedData: SpeedData[];
}

export interface Workout {
  id: string;
  type: string;
  distance: string;
  duration: string;
  calories: string;
  pace: string;
  startTime: Date;
  endTime: Date | null;
  routeId: number | null;
  currentLocation: {
    latitude: number;
    longitude: number;
  };
  routeCoordinates: {
    latitude: number;
    longitude: number;
  }[];
}