import React, { createContext, useContext, useState, useEffect } from 'react';
import { LatLng } from 'react-native-maps';
import { StepsData, SpeedData, Workout, DailyStats, WalkingStats } from '@/types/fitness';

interface FitnessContextType {
  dailyStats: DailyStats;
  walkingStats: WalkingStats;
  currentWorkout: Workout | null;
  selectedDay: number;
  setSelectedDay: (day: number) => void;
  startWorkout: (options: { type: string; routeId: number | null; startTime: Date }) => void;
  stopWorkout: () => void;
}

const FitnessContext = createContext<FitnessContextType | undefined>(undefined);

export const useFitness = () => {
  const context = useContext(FitnessContext);
  if (!context) {
    throw new Error('useFitness must be used within a FitnessProvider');
  }
  return context;
};

// Mock data
const mockDailyStats: DailyStats = {
  steps: 4754,
  stepsGoal: 10000,
  stepsPercentage: 47.54,
  distance: 3.5,
  calories: 253,
  activeMinutes: 45,
};

const mockStepsData: StepsData[] = [
  { hour: '05:00', steps: 800 },
  { hour: '06:00', steps: 2400 },
  { hour: '07:00', steps: 3200 },
  { hour: '08:00', steps: 3800 },
];

const mockCaloriesData = [
  { value: 150 },
  { value: 220 },
  { value: 180 },
  { value: 290 },
  { value: 350 },
  { value: 280 },
  { value: 320 },
];

const mockSpeedData: SpeedData[] = [
  { value: 1, color: '#E5F9F2' },
  { value: 1, color: '#CAF3E5' },
  { value: 1, color: '#A8EDDA' },
  { value: 1, color: '#8FE7CF' },
  { value: 1, color: '#66DDC2' },
  { value: 1, color: '#3DD3B4' },
  { value: 1, color: '#18C8A7' },
  { value: 1, color: '#00C48C' },
];

const mockCurrentWorkout: Workout = {
  id: '1',
  type: 'running',
  distance: '3.42',
  duration: '0:37:21',
  calories: '75.5',
  pace: '4:30',
  startTime: new Date(),
  endTime: null,
  routeId: 1,
  currentLocation: {
    latitude: 37.7749,
    longitude: -122.4194,
  },
  routeCoordinates: [
    { latitude: 37.7749, longitude: -122.4194 },
    { latitude: 37.7748, longitude: -122.4180 },
    { latitude: 37.7747, longitude: -122.4170 },
    { latitude: 37.7750, longitude: -122.4160 },
  ],
};

const mockWalkingStats: WalkingStats = {
  totalDistance: '142.47',
  totalSteps: '475.47',
  calories: '957',
  speed: '6.2',
  stepsData: mockStepsData,
  caloriesData: mockCaloriesData,
  speedData: mockSpeedData,
};

export const FitnessProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [dailyStats, setDailyStats] = useState<DailyStats>(mockDailyStats);
  const [walkingStats, setWalkingStats] = useState<WalkingStats>(mockWalkingStats);
  const [currentWorkout, setCurrentWorkout] = useState<Workout | null>(mockCurrentWorkout);
  const [selectedDay, setSelectedDay] = useState(2); // Tuesday by default

  const startWorkout = (options: { type: string; routeId: number | null; startTime: Date }) => {
    const newWorkout: Workout = {
      id: Math.random().toString(36).substring(2, 9),
      type: options.type,
      distance: '0.00',
      duration: '0:00:00',
      calories: '0',
      pace: '0:00',
      startTime: options.startTime,
      endTime: null,
      routeId: options.routeId,
      currentLocation: {
        latitude: 37.7749,
        longitude: -122.4194,
      },
      routeCoordinates: [],
    };
    
    setCurrentWorkout(newWorkout);
  };

  const stopWorkout = () => {
    if (currentWorkout) {
      setCurrentWorkout({
        ...currentWorkout,
        endTime: new Date(),
      });
      
      // In a real app, we would save the workout to storage/backend
      
      // Reset current workout
      setTimeout(() => {
        setCurrentWorkout(null);
      }, 1000);
    }
  };

  return (
    <FitnessContext.Provider
      value={{
        dailyStats,
        walkingStats,
        currentWorkout,
        selectedDay,
        setSelectedDay,
        startWorkout,
        stopWorkout,
      }}
    >
      {children}
    </FitnessContext.Provider>
  );
};