import { StyleSheet, View, Text, Image, TouchableOpacity, ScrollView, Switch } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useAuth } from '@/contexts/AuthContext';
import { useState } from 'react';
import { Settings, Award, Bell, ChevronRight, LogOut, Shield, User } from 'lucide-react-native';

export default function ProfileScreen() {
  const { user, logout } = useAuth();
  const [notifications, setNotifications] = useState(true);
  
  const menuItems = [
    { 
      id: 'personal',
      title: 'Personal Information',
      icon: <User size={20} color="#00C48C" />,
      iconBg: 'rgba(0, 196, 140, 0.1)'
    },
    { 
      id: 'goals',
      title: 'Goals & Achievements',
      icon: <Award size={20} color="#FF9500" />,
      iconBg: 'rgba(255, 149, 0, 0.1)'
    },
    { 
      id: 'notifications',
      title: 'Notifications',
      icon: <Bell size={20} color="#007AFF" />,
      iconBg: 'rgba(0, 122, 255, 0.1)',
      toggle: true,
      value: notifications,
      onToggle: () => setNotifications(!notifications)
    },
    { 
      id: 'privacy',
      title: 'Privacy & Security',
      icon: <Shield size={20} color="#FF3B30" />,
      iconBg: 'rgba(255, 59, 48, 0.1)'
    },
    { 
      id: 'settings',
      title: 'App Settings',
      icon: <Settings size={20} color="#5856D6" />,
      iconBg: 'rgba(88, 86, 214, 0.1)'
    },
  ];
  
  return (
    <SafeAreaView style={styles.container} edges={['top']}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Profile</Text>
      </View>
      
      <ScrollView contentContainerStyle={styles.content}>
        <View style={styles.profileCard}>
          <Image 
            source={{ uri: user?.profileImage || 'https://images.pexels.com/photos/614810/pexels-photo-614810.jpeg' }}
            style={styles.profileImage}
          />
          <View style={styles.profileInfo}>
            <Text style={styles.profileName}>{user?.name}</Text>
            <Text style={styles.profileEmail}>{user?.email}</Text>
          </View>
          <TouchableOpacity style={styles.editButton}>
            <Text style={styles.editButtonText}>Edit</Text>
          </TouchableOpacity>
        </View>
        
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>152</Text>
            <Text style={styles.statLabel}>Workouts</Text>
          </View>
          <View style={styles.statDivider} />
          <View style={styles.statItem}>
            <Text style={styles.statValue}>845</Text>
            <Text style={styles.statLabel}>km</Text>
          </View>
          <View style={styles.statDivider} />
          <View style={styles.statItem}>
            <Text style={styles.statValue}>37</Text>
            <Text style={styles.statLabel}>Achievements</Text>
          </View>
        </View>
        
        <View style={styles.menuContainer}>
          {menuItems.map((item) => (
            <TouchableOpacity 
              key={item.id}
              style={styles.menuItem}
              onPress={item.toggle ? undefined : () => {}}
            >
              <View style={[styles.menuIconContainer, { backgroundColor: item.iconBg }]}>
                {item.icon}
              </View>
              <Text style={styles.menuItemText}>{item.title}</Text>
              {item.toggle ? (
                <Switch
                  value={item.value}
                  onValueChange={item.onToggle}
                  trackColor={{ false: '#e0e0e0', true: '#00C48C' }}
                  thumbColor="#fff"
                />
              ) : (
                <ChevronRight size={20} color="#ccc" />
              )}
            </TouchableOpacity>
          ))}
        </View>
        
        <TouchableOpacity style={styles.logoutButton} onPress={logout}>
          <LogOut size={20} color="#FF3B30" />
          <Text style={styles.logoutText}>Log Out</Text>
        </TouchableOpacity>
        
        <Text style={styles.versionText}>Version 1.0.0</Text>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  header: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  headerTitle: {
    fontSize: 20,
    fontFamily: 'InterBold',
    color: '#333',
    textAlign: 'center',
  },
  content: {
    padding: 16,
    paddingBottom: 80,
  },
  profileCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f9f9f9',
    borderRadius: 16,
    padding: 16,
    marginBottom: 24,
  },
  profileImage: {
    width: 64,
    height: 64,
    borderRadius: 32,
    marginRight: 16,
  },
  profileInfo: {
    flex: 1,
  },
  profileName: {
    fontSize: 18,
    fontFamily: 'InterBold',
    color: '#333',
    marginBottom: 4,
  },
  profileEmail: {
    fontSize: 14,
    fontFamily: 'InterRegular',
    color: '#666',
  },
  editButton: {
    backgroundColor: '#00C48C',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  editButtonText: {
    color: '#fff',
    fontFamily: 'InterMedium',
    fontSize: 14,
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: '#f9f9f9',
    borderRadius: 16,
    padding: 16,
    marginBottom: 24,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 22,
    fontFamily: 'InterBold',
    color: '#333',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 14,
    fontFamily: 'InterRegular',
    color: '#666',
  },
  statDivider: {
    width: 1,
    height: '80%',
    backgroundColor: '#e0e0e0',
    alignSelf: 'center',
  },
  menuContainer: {
    backgroundColor: '#f9f9f9',
    borderRadius: 16,
    marginBottom: 24,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  menuIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  menuItemText: {
    flex: 1,
    fontSize: 16,
    fontFamily: 'InterMedium',
    color: '#333',
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: 'rgba(255, 59, 48, 0.1)',
    borderRadius: 16,
    padding: 16,
    marginBottom: 24,
  },
  logoutText: {
    fontSize: 16,
    fontFamily: 'InterMedium',
    color: '#FF3B30',
    marginLeft: 8,
  },
  versionText: {
    fontSize: 14,
    fontFamily: 'InterRegular',
    color: '#999',
    textAlign: 'center',
  },
});