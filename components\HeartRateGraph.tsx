import React from 'react';
import { View, StyleSheet } from 'react-native';
import Svg, { Path, Defs, LinearGradient, Stop } from 'react-native-svg';
import { line, curveBasis } from 'd3-shape';

const HeartRateGraph: React.FC = () => {
  // Sample ECG-like data
  const data = [
    { x: 0, y: 50 },
    { x: 10, y: 50 },
    { x: 15, y: 20 },
    { x: 20, y: 100 },
    { x: 25, y: 20 },
    { x: 30, y: 50 },
    { x: 40, y: 50 },
    { x: 45, y: 20 },
    { x: 50, y: 100 },
    { x: 55, y: 20 },
    { x: 60, y: 50 },
    { x: 70, y: 50 },
    { x: 75, y: 20 },
    { x: 80, y: 100 },
    { x: 85, y: 20 },
    { x: 90, y: 50 },
    { x: 100, y: 50 },
  ];

  const width = 140;
  const height = 40;

  const path = line<{ x: number, y: number }>()
    .x(d => (d.x / 100) * width)
    .y(d => (d.y / 100) * height)
    .curve(curveBasis)(data);

  return (
    <View style={styles.container}>
      <Svg width={width} height={height}>
        <Defs>
          <LinearGradient id="grad" x1="0" y1="0" x2="0" y2="1">
            <Stop offset="0" stopColor="#00C48C" stopOpacity="1" />
            <Stop offset="1" stopColor="#00C48C" stopOpacity="0.3" />
          </LinearGradient>
        </Defs>
        <Path
          d={path || ''}
          stroke="#00C48C"
          strokeWidth={2}
          fill="none"
        />
      </Svg>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    height: 40,
    justifyContent: 'center',
  },
});

export default HeartRateGraph;