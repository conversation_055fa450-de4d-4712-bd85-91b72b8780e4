import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { MapPin, Clock, Zap } from 'lucide-react-native';
import { Workout } from '@/types/fitness';

interface WorkoutInfoProps {
  workout: Workout;
}

const WorkoutInfo: React.FC<WorkoutInfoProps> = ({ workout }) => {
  return (
    <View style={styles.container}>
      <View style={styles.metric}>
        <Text style={styles.metricLabel}>Distance</Text>
        <Text style={styles.metricValue}>{workout.distance}</Text>
        <Text style={styles.metricUnit}>km</Text>
      </View>
      <View style={styles.metric}>
        <Text style={styles.metricLabel}>Duration</Text>
        <Text style={styles.metricValue}>{workout.duration}</Text>
      </View>
      <View style={styles.metric}>
        <Text style={styles.metricLabel}>Calories</Text>
        <Text style={styles.metricValue}>{workout.calories}</Text>
        <Text style={styles.metricUnit}>kcal</Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    left: 16,
    right: 16,
    top: 120,
    flexDirection: 'row',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 16,
    padding: 16,
  },
  metric: {
    flex: 1,
    alignItems: 'center',
  },
  metricLabel: {
    color: '#fff',
    opacity: 0.8,
    fontSize: 12,
    fontFamily: 'InterRegular',
    marginBottom: 4,
  },
  metricValue: {
    color: '#fff',
    fontSize: 18,
    fontFamily: 'InterBold',
  },
  metricUnit: {
    color: '#fff',
    opacity: 0.8,
    fontSize: 12,
    fontFamily: 'InterRegular',
    position: 'absolute',
    right: 14,
    bottom: 0,
  },
});

export default WorkoutInfo;