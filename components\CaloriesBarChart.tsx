import React from 'react';
import { View, StyleSheet } from 'react-native';

interface CaloriesData {
  value: number;
}

interface CaloriesBarChartProps {
  data: CaloriesData[];
}

const CaloriesBarChart: React.FC<CaloriesBarChartProps> = ({ data }) => {
  const maxValue = Math.max(...data.map(d => d.value));

  return (
    <View style={styles.container}>
      {data.map((item, index) => (
        <View 
          key={index}
          style={[
            styles.bar,
            { 
              height: (item.value / maxValue) * 30,
              backgroundColor: '#FF5B5B',
              opacity: 0.6 + (item.value / maxValue) * 0.4,
            }
          ]}
        />
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    height: 30,
    alignItems: 'flex-end',
    justifyContent: 'space-between',
  },
  bar: {
    width: 6,
    borderRadius: 3,
    minHeight: 2,
  },
});

export default CaloriesBarChart;